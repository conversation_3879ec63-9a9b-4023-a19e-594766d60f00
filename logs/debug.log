[2m2025-07-30 00:06:43.966[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:06:44.005[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:06:44.007[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:06:44.013[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:06:44.014[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:06:44.023[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:06:44.045[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:44.051[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:44.052[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:44.053[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:44.053[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:06:44.058[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:06:59.318[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:06:59.341[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:06:59.344[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:06:59.345[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:06:59.348[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:06:59.361[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:06:59.392[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:59.400[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:59.402[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:59.403[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:06:59.403[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:06:59.408[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:08:58.470[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:08:58.477[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:08:58.478[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:08:58.485[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:08:58.485[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:08:58.495[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:08:58.519[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:08:58.525[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:08:58.527[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:08:58.527[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:08:58.527[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:08:58.532[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:09:31.988[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:31.995[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:31.997[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:31.998[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:31.998[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:09:32.009[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:09:32.032[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:32.037[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:32.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:32.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:32.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:09:32.045[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:09:49.710[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:49.726[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:49.726[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:49.732[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:49.733[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:09:49.746[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:09:49.781[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:49.790[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:49.790[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:49.793[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:49.793[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:09:49.802[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:09:52.778[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:52.791[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:09:52.791[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:52.795[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:09:52.796[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:09:52.809[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:09:52.858[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:52.870[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:52.873[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:52.873[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:09:52.873[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:09:52.883[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:11:00.260[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:00.272[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:00.274[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:00.280[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:00.280[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:11:00.297[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:11:00.326[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:00.331[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:00.333[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:00.333[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:00.333[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:11:00.339[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:11:02.967[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:02.977[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:02.989[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:02.995[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:02.996[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:11:03.006[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:11:03.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:03.045[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:03.047[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:03.047[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:03.048[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:11:03.053[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:11:09.982[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:09.990[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:11:09.992[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:09.992[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:11:09.993[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:11:10.006[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:11:10.032[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:10.038[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:10.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:10.040[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:11:10.040[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:11:10.045[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:12:57.918[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:12:57.930[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:12:57.933[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:12:57.939[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:12:57.940[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:12:57.952[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:12:58.008[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:12:58.015[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:12:58.017[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:12:58.018[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:12:58.019[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:12:58.029[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:13:20.555[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:20.565[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:20.567[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:20.569[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:20.569[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:13:20.583[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:13:20.617[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:20.625[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:20.627[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:20.628[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:20.629[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:13:20.641[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:13:32.845[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:32.866[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:32.866[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:32.869[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:32.869[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:13:32.883[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:13:32.921[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:32.931[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:32.934[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:32.935[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:32.935[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:13:32.942[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:13:48.096[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:48.103[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:13:48.105[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:48.106[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:13:48.107[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:13:48.125[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:13:48.170[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:48.176[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:48.178[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:48.179[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:13:48.180[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:13:48.186[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:14:00.084[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:00.099[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:00.099[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:00.099[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:00.106[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:14:00.123[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:14:00.171[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:00.181[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:00.183[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:00.184[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:00.185[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:14:00.192[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:14:13.699[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:13.709[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:13.711[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:13.712[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:13.712[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:14:13.726[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:14:13.753[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:13.760[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:13.761[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:13.762[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:13.762[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:14:13.769[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:14:30.693[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:30.700[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:14:30.702[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:30.702[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:14:30.703[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:14:30.716[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:14:30.747[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:30.753[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:30.755[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:30.755[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:14:30.755[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:14:30.762[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:16:07.978[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:16:07.988[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:16:07.990[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:16:07.997[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:16:07.998[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:16:08.012[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:16:08.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:16:08.045[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:16:08.047[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:16:08.047[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:16:08.047[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:16:08.054[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:18:38.918[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:18:38.928[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:18:38.930[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:18:38.938[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:18:38.939[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:18:38.951[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:18:38.984[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:38.990[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:38.992[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:38.992[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:38.992[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:18:38.998[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:18:51.585[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:18:51.594[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:18:51.594[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:18:51.601[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:18:51.602[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:18:51.619[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:18:51.657[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:51.667[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:51.667[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:51.670[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:18:51.670[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:18:51.677[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:19:20.296[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:19:20.341[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:19:20.346[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:19:20.346[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:19:20.347[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:19:20.361[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:19:20.398[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:20.407[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:20.409[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:20.410[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:20.411[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:19:20.417[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:19:31.973[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:19:31.996[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:19:31.996[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:19:31.997[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:19:31.998[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:19:32.014[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:19:32.051[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:32.060[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:32.063[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:32.063[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:19:32.063[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:19:32.071[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:20:10.259[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:20:10.274[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:20:10.277[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:20:10.278[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:20:10.278[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:20:10.291[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:20:10.327[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:20:10.335[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:20:10.335[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:20:10.335[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:20:10.335[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:20:10.341[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:21:19.071[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:19.084[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:19.086[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:19.100[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:19.101[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:19.113[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:19.142[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:21:19.149[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,FIELD_NAME,FIELD_TYPE,DISPLAY_NAME,CATEGORY,SORT_ORDER,IS_ENABLED,DESCRIPTION,CREATE_TIME,CREATE_USER,UPDATE_TIME,UPDATE_USER    FROM  FIELD_DEFINITION         WHERE  (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:21:19.152[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:21:19.152[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, FIELD_NAME, FIELD_TYPE, DISPLAY_NAME, CATEGORY, SORT_ORDER, IS_ENABLED, DESCRIPTION, CREATE_TIME, CREATE_USER, UPDATE_TIME, UPDATE_USER FROM FIELD_DEFINITION WHERE (IS_ENABLED = ?) ORDER BY SORT_ORDER ASC
[2m2025-07-30 00:21:19.152[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m ==> Parameters: true(Boolean)
[2m2025-07-30 00:21:19.160[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.F.selectList                  [0;39m [2m:[0;39m <==      Total: 6
[2m2025-07-30 00:21:40.735[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-30 00:21:40.746[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-30 00:21:40.747[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-30 00:21:40.747[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-30 00:21:40.752[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), SYSTEM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"68","right":444,"bottom":43.5,"vCenter":406.5,"hCenter":22.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":1.5,"height":58.5,"width":66,"title":"二维码","qrcodeType":"qrcode","right":525.75,"bottom":53.25,"vCenter":495,"hCenter":27.75,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"field":"{qrcode}","testData":"qrcode"},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125,"field":"{bankName}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":450,"top":12,"height":40.5,"width":9,"title":"EPQ","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{specialMark}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":27,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{coinName1}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"Superb Gem Unc","right":439.********,"bottom":61.********,"vCenter":405.********,"hCenter":55.********,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"钱币编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{serialNumber}-{version}"},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-30T00:21:40.726052(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-30 00:21:40.778[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-30 00:21:40.818[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:40.826[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:40.828[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:40.828[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:40.828[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:40.839[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:45.898[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-30 00:21:45.905[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: UPDATE LABEL_TEMPLATE  SET TEMPLATE_NAME=?, TEMPLATE_TYPE=?, LAYOUT_CONFIG=?,    IS_DEFAULT=?,   UPDATE_TIME=?, STATUS=?  WHERE ID=?
[2m2025-07-30 00:21:45.907[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-30 00:21:45.908[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==>  Preparing: UPDATE LABEL_TEMPLATE SET TEMPLATE_NAME = ?, TEMPLATE_TYPE = ?, LAYOUT_CONFIG = ?, IS_DEFAULT = ?, UPDATE_TIME = ?, STATUS = ? WHERE ID = ?
[2m2025-07-30 00:21:45.908[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m ==> Parameters: 大签191x26(String), CUSTOM(String), {"panels":[{"index":0,"name":1,"height":26,"width":191,"paperHeader":0,"paperFooter":73.70078740157481,"printElements":[{"options":{"left":0,"top":-1.5,"height":72,"width":114},"printElementType":{"title":"矩形","type":"rect"}},{"options":{"left":369,"top":1.5,"height":42,"width":75,"title":"68","right":444,"bottom":43.5,"vCenter":406.5,"hCenter":22.5,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":465,"top":1.5,"height":58.5,"width":66,"title":"二维码","qrcodeType":"qrcode","right":525.75,"bottom":53.25,"vCenter":495,"hCenter":27.75,"qrCodeLevel":0,"coordinateSync":false,"widthHeightSync":false,"field":"{qrcode}","testData":"qrcode"},"printElementType":{"title":"二维码","type":"qrcode"}},{"options":{"left":114,"top":6,"height":9.75,"width":141,"title":"银行名称","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"right":254.25,"bottom":15,"vCenter":183.75,"hCenter":10.125,"field":"{bankName}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":450,"top":12,"height":40.5,"width":9,"title":"EPQ","right":458.25,"bottom":52.5,"vCenter":453.75,"hCenter":32.25,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{specialMark}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":27,"height":9.75,"width":139.5,"title":"钱币名称1","coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{coinName1}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":369,"top":49.5,"height":12,"width":75,"title":"Superb Gem Unc","right":439.********,"bottom":61.********,"vCenter":405.********,"hCenter":55.********,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{gradeScore}"},"printElementType":{"title":"文本","type":"text"}},{"options":{"left":115.5,"top":49.5,"height":9.75,"width":139.5,"title":"钱币编号-版别","right":234.75,"bottom":59.25,"vCenter":174.75,"hCenter":54.375,"coordinateSync":false,"widthHeightSync":false,"qrCodeLevel":0,"field":"{serialNumber}-{version}"},"printElementType":{"title":"文本","type":"text"}}],"paperNumberLeft":388.5,"paperNumberTop":51,"paperNumberContinue":true,"watermarkOptions":{},"panelLayoutOptions":{}}]}(String), false(Boolean), 2025-07-30T00:21:45.897687(LocalDateTime), ACTIVE(String), cb6b4723a01c7e32a8e175d965ac3ef4(String)
[2m2025-07-30 00:21:45.928[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.updateById                  [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-07-30 00:21:45.958[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:45.964[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:45.965[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:45.966[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:45.966[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:45.977[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:47.740[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:47.746[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:47.746[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:47.748[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:47.749[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:47.761[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:47.779[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:47.785[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:47.787[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:47.787[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:47.788[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:47.797[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:50.406[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-07-30 00:21:50.421[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-07-30 00:21:50.423[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-07-30 00:21:50.441[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-07-30 00:21:50.441[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 00:21:50.446[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:50.463[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-30 00:21:50.463[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-07-30 00:21:50.472[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-3][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:52.114[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:52.120[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:52.122[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:52.122[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:52.122[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:52.134[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-4][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:52.160[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:52.167[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,LAYOUT_CONFIG,FIELD_MAPPING,COLOR_CONFIG,PAGE_SETTINGS,IS_DEFAULT,CREATE_USER,CREATE_TIME,UPDATE_TIME,STATUS    FROM  LABEL_TEMPLATE         WHERE  (STATUS = ?) ORDER BY IS_DEFAULT DESC,CREATE_TIME DESC
[2m2025-07-30 00:21:52.169[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:52.169[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, LAYOUT_CONFIG, FIELD_MAPPING, COLOR_CONFIG, PAGE_SETTINGS, IS_DEFAULT, CREATE_USER, CREATE_TIME, UPDATE_TIME, STATUS FROM LABEL_TEMPLATE WHERE (STATUS = ?) ORDER BY IS_DEFAULT DESC, CREATE_TIME DESC
[2m2025-07-30 00:21:52.170[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m ==> Parameters: ACTIVE(String)
[2m2025-07-30 00:21:52.182[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.L.selectList                  [0;39m [2m:[0;39m <==      Total: 2
[2m2025-07-30 00:21:53.445[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:53.463[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:53.466[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:53.481[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM_ITEM WHERE (AUTHENTICITY IS NOT NULL)
[2m2025-07-30 00:21:53.481[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-07-30 00:21:53.490[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:53.491[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-30 00:21:53.491[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 20(Long), 0(Long)
[2m2025-07-30 00:21:53.505[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-30 00:21:53.517[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.528[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.530[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.530[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.530[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:53.537[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:53.538[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.550[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.552[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.552[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.552[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:53.558[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:53.559[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.570[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.572[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.573[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.573[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:53.579[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:53.579[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.591[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.593[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.593[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.593[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:53.602[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:53.602[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.614[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:53.616[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.616[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:53.617[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:53.622[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-1][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.018[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:55.036[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:55.039[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC
[2m2025-07-30 00:21:55.056[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL)
[2m2025-07-30 00:21:55.056[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.062[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.062[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (SENDNUM IN (?) AND AUTHENTICITY IS NOT NULL) ORDER BY CREATE_TIME DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-07-30 00:21:55.062[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: ************(String), 20(Long), 0(Long)
[2m2025-07-30 00:21:55.068[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-30 00:21:55.069[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.084[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.086[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.086[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.086[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.092[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.093[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.106[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.107[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.108[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.108[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.113[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.114[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.126[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.128[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.128[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.128[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.137[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.138[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.149[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.151[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.151[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.151[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.157[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:21:55.158[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.170[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:21:55.171[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.172[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:21:55.172[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:21:55.178[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[io-7070-exec-10][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:00.761[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:22:00.777[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:22:00.779[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:22:00.780[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:22:00.780[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-07-30 00:22:00.787[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-30 00:22:00.790[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-07-30 00:22:00.804[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM IN (?))
[2m2025-07-30 00:22:00.806[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-07-30 00:22:00.806[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM IN (?))
[2m2025-07-30 00:22:00.806[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:00.812[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.088[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:22:02.105[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:22:02.108[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:22:02.108[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:22:02.109[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-07-30 00:22:02.126[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-30 00:22:02.129[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.142[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.144[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.144[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.144[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.150[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.152[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.164[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.165[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.165[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.165[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.171[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.171[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.182[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.183[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.184[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.184[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.199[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.199[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.211[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.213[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.213[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.213[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.220[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.221[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.231[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.233[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.233[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.233[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.239[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.239[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.250[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.251[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.251[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.251[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.256[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.257[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.267[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.269[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.269[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.269[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.275[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.275[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.286[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.287[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.287[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.287[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.295[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.296[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.307[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.308[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.309[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.309[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.314[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:22:02.314[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.325[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:22:02.326[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.327[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:22:02.327[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:22:02.332[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-9][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.183[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:23:22.208[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,SEQNO,COIN_TYPE,COIN_YEAR AS year,FACE_VALUE,COIN_VERSION AS version,QUANTITY,GRADE_FEE,REMARK,DIY_CODE,SERIAL_NUMBER,COIN_NAME1,COIN_NAME2,COIN_NAME3,FACE_VAL,BELONG_NAME,WEIGHT_NAME,MATERIAL,BOX_TYPE,STANDARD_PRICE,INTERNATIONAL_PRICE,FEE,DISCOUNT,COIN_SIZE,COIN_WEIGHT,YEAR_INFO,REGION,TAX_TYPE,BOX_FEE,URGENT_FEE,CATALOG,RANK,BANK_NAME,SPECIAL_MARK,INTERNAL_NOTE,EXTERNAL_NOTE,SPECIAL_LABEL,AUTHENTICITY,GRADE_SCORE,SCORE_REMARKS,INSPECTION_NOTE,COIN_IMAGES,CREATE_TIME    FROM  PJ_O_SENDFORM_ITEM         WHERE  (ID IN (?,?,?,?,?))
[2m2025-07-30 00:23:22.210[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:23:22.217[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, SEQNO, COIN_TYPE, COIN_YEAR AS year, FACE_VALUE, COIN_VERSION AS version, QUANTITY, GRADE_FEE, REMARK, DIY_CODE, SERIAL_NUMBER, COIN_NAME1, COIN_NAME2, COIN_NAME3, FACE_VAL, BELONG_NAME, WEIGHT_NAME, MATERIAL, BOX_TYPE, STANDARD_PRICE, INTERNATIONAL_PRICE, FEE, DISCOUNT, COIN_SIZE, COIN_WEIGHT, YEAR_INFO, REGION, TAX_TYPE, BOX_FEE, URGENT_FEE, CATALOG, RANK, BANK_NAME, SPECIAL_MARK, INTERNAL_NOTE, EXTERNAL_NOTE, SPECIAL_LABEL, AUTHENTICITY, GRADE_SCORE, SCORE_REMARKS, INSPECTION_NOTE, COIN_IMAGES, CREATE_TIME FROM PJ_O_SENDFORM_ITEM WHERE (ID IN (?, ?, ?, ?, ?))
[2m2025-07-30 00:23:22.218[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m ==> Parameters: 5b26701afbfb65c0a8a833f45581d752(String), 110de26c9970764fffbd6af7011d2768(String), f3de6989e3d445c00fa9c159a6dbdba0(String), 247550797bd7aa10784121f51394f84b(String), 811424d68b9aea59e781950a5a47c736(String)
[2m2025-07-30 00:23:22.232[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.P.selectList                  [0;39m [2m:[0;39m <==      Total: 5
[2m2025-07-30 00:23:22.244[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.258[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.260[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.260[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.260[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.267[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.268[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.280[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.282[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.282[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.282[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.288[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.289[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.301[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.302[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.302[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.302[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.309[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.310[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.320[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.322[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.322[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.322[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.329[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.330[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.341[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.342[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.342[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.343[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.350[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.350[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.361[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.363[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.363[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.363[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.369[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.370[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.381[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.383[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.383[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.383[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.390[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.390[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.402[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.403[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.404[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.404[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.411[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.411[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.423[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.428[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.428[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.429[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.435[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-07-30 00:23:22.435[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.447[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM         WHERE  (SENDNUM = ?)
[2m2025-07-30 00:23:22.449[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.449[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM WHERE (SENDNUM = ?)
[2m2025-07-30 00:23:22.449[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: ************(String)
[2m2025-07-30 00:23:22.456[0;39m [32mDEBUG[0;39m [35m3916[0;39m [2m---[0;39m [2m[nio-7070-exec-7][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
